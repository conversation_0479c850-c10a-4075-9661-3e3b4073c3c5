import React from 'react';
import { AppBar, Toolbar, Box, IconButton } from '@mui/material';
import { styled } from '@mui/material/styles';
import HomeIcon from '@mui/icons-material/Home';
import AccountCircleIcon from '@mui/icons-material/AccountCircle';
import SystemLogo from './SystemLogo';
import NavigationMenu from './NavigationMenu';

const StyledAppBar = styled(AppBar)(({ theme }) => ({
  backgroundColor: theme.palette.background.paper,
  boxShadow: 'none',
  borderBottom: `1px solid ${theme.palette.grey[800]}`,
}));

const HeaderActions = styled(Box)(({ theme }) => ({
  display: 'flex',
  alignItems: 'center',
  gap: theme.spacing(1),
}));

interface HeaderNavigationProps {
  systemTitle: string;
  systemSubtitle: string;
  onTabChange?: (value: number) => void;
}

const HeaderNavigation: React.FC<HeaderNavigationProps> = ({
  systemTitle,
  systemSubtitle,
  onTabChange
}) => {
  return (
    <StyledAppBar position="static">
      <Toolbar sx={{ justifyContent: 'space-between', minHeight: '64px !important' }}>
        <SystemLogo title={systemTitle} subtitle={systemSubtitle} />
        
        <Box sx={{ flex: 1, display: 'flex', justifyContent: 'center' }}>
          <NavigationMenu onTabChange={onTabChange} />
        </Box>
        
        <HeaderActions>
          <IconButton color="primary" size="small">
            <HomeIcon />
          </IconButton>
          <IconButton color="primary" size="small">
            <AccountCircleIcon />
          </IconButton>
        </HeaderActions>
      </Toolbar>
    </StyledAppBar>
  );
};

export default HeaderNavigation;
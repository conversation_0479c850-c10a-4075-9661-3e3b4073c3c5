import React from 'react';
import { Box, Stack } from '@mui/material';
import { styled } from '@mui/material/styles';
import SearchIcon from '@mui/icons-material/Search';
import LayersIcon from '@mui/icons-material/Layers';
import ZoomInIcon from '@mui/icons-material/ZoomIn';
import ZoomOutIcon from '@mui/icons-material/ZoomOut';
import ControlButton from './ControlButton';

const SidePanelContainer = styled(Box)(({ theme }) => ({
  position: 'absolute',
  top: '50%',
  right: theme.spacing(2),
  transform: 'translateY(-50%)',
  zIndex: 1000,
}));

interface SidePanelProps {
  onSearch?: () => void;
  onLayersToggle?: () => void;
  onZoomIn?: () => void;
  onZoomOut?: () => void;
}

const SidePanel: React.FC<SidePanelProps> = ({
  onSearch,
  onLayersToggle,
  onZoomIn,
  onZoomOut
}) => {
  return (
    <SidePanelContainer>
      <Stack direction="column" spacing={1}>
        <ControlButton
          icon={<SearchIcon />}
          tooltip="搜索"
          onClick={onSearch}
        />
        <ControlButton
          icon={<LayersIcon />}
          tooltip="图层管理"
          onClick={onLayersToggle}
        />
        <ControlButton
          icon={<ZoomInIcon />}
          tooltip="放大"
          onClick={onZoomIn}
        />
        <ControlButton
          icon={<ZoomOutIcon />}
          tooltip="缩小"
          onClick={onZoomOut}
        />
      </Stack>
    </SidePanelContainer>
  );
};

export default SidePanel;
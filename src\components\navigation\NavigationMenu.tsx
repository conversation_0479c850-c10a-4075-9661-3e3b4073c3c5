import React, { useState } from 'react';
import { Tabs, Tab, Box } from '@mui/material';
import { styled } from '@mui/material/styles';

const StyledTabs = styled(Tabs)(({ theme }) => ({
  '& .MuiTabs-indicator': {
    backgroundColor: theme.palette.primary.main,
    height: 3,
  },
  '& .MuiTab-root': {
    color: theme.palette.text.secondary,
    fontSize: '0.9rem',
    fontWeight: 400,
    minWidth: 'auto',
    padding: theme.spacing(1, 2),
    '&.Mui-selected': {
      color: theme.palette.primary.main,
    },
    '&:hover': {
      color: theme.palette.primary.light,
    },
  },
}));

interface NavigationMenuProps {
  onTabChange?: (value: number) => void;
}

const NavigationMenu: React.FC<NavigationMenuProps> = ({ onTabChange }) => {
  const [activeTab, setActiveTab] = useState(0);

  const handleChange = (event: React.SyntheticEvent, newValue: number) => {
    setActiveTab(newValue);
    onTabChange?.(newValue);
  };

  const menuItems = [
    '场景管理',
    '服务管理', 
    '像控点管理',
    '成果统计'
  ];

  return (
    <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
      <StyledTabs value={activeTab} onChange={handleChange}>
        {menuItems.map((item, index) => (
          <Tab key={index} label={item} />
        ))}
      </StyledTabs>
    </Box>
  );
};

export default NavigationMenu;
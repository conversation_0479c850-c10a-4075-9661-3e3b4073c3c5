import React from 'react';
import { Box, Typography } from '@mui/material';
import { styled } from '@mui/material/styles';

const LogoContainer = styled(Box)(({ theme }) => ({
  display: 'flex',
  alignItems: 'center',
  gap: theme.spacing(1),
}));

const LogoIcon = styled(Box)(({ theme }) => ({
  width: 32,
  height: 32,
  borderRadius: '50%',
  background: `linear-gradient(135deg, ${theme.palette.primary.main}, ${theme.palette.primary.light})`,
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
  color: theme.palette.primary.contrastText,
  fontSize: '1.2rem',
  fontWeight: 'bold',
}));

interface SystemLogoProps {
  title: string;
  subtitle: string;
}

const SystemLogo: React.FC<SystemLogoProps> = ({ title, subtitle }) => {
  return (
    <LogoContainer>
      <LogoIcon>3D</LogoIcon>
      <Box>
        <Typography variant="h6" sx={{ lineHeight: 1.2, fontSize: '1rem' }}>
          {title}
        </Typography>
        <Typography variant="caption" sx={{ opacity: 0.8, fontSize: '0.7rem' }}>
          {subtitle}
        </Typography>
      </Box>
    </LogoContainer>
  );
};

export default SystemLogo;
import React from 'react';
import { ThemeProvider } from '@mui/material/styles';
import { CssBaseline } from '@mui/material';
import Dashboard from './src/components/Dashboard';
import theme from './src/theme/theme';
import { mockRootProps } from './src/data/cesiumDashboardMockData';

const App: React.FC = () => {
  return (
    <ThemeProvider theme={theme}>
      <CssBaseline />
      <Dashboard
        systemTitle={mockRootProps.systemTitle}
        systemSubtitle={mockRootProps.systemSubtitle}
        initialMapCenter={mockRootProps.initialMapCenter}
        initialZoom={mockRootProps.initialZoom}
      />
    </ThemeProvider>
  );
};

export default App;
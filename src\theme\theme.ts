// Theme configuration for the 3D data management system
import { createTheme } from '@mui/material/styles';

const theme = createTheme({
  palette: {
    mode: 'dark',
    primary: {
      main: '#00BCD4', // Cyan blue for primary elements
      light: '#4DD0E1',
      dark: '#0097A7',
      contrastText: '#FFFFFF'
    },
    secondary: {
      main: '#FFC107', // Yellow for polygon highlights
      light: '#FFEB3B',
      dark: '#FF8F00',
      contrastText: '#000000'
    },
    background: {
      default: '#1A1A1A', // Dark background for map interface
      paper: '#2D2D2D'
    },
    text: {
      primary: '#FFFFFF',
      secondary: '#B0BEC5'
    },
    grey: {
      50: '#FAFAFA',
      100: '#F5F5F5',
      200: '#EEEEEE',
      300: '#E0E0E0',
      400: '#BDBDBD',
      500: '#9E9E9E',
      600: '#757575',
      700: '#616161',
      800: '#424242',
      900: '#212121'
    }
  },
  typography: {
    fontFamily: '"Roboto", "Microsoft YaHei", "SimHei", sans-serif',
    h4: {
      fontSize: '1.5rem',
      fontWeight: 500,
      color: '#FFFFFF'
    },
    h6: {
      fontSize: '1.1rem',
      fontWeight: 400,
      color: '#FFFFFF'
    },
    body1: {
      fontSize: '0.9rem',
      color: '#B0BEC5'
    },
    caption: {
      fontSize: '0.75rem',
      color: '#9E9E9E'
    }
  },
  shape: {
    borderRadius: 4
  }
});

export default theme;
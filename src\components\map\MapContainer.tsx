import React, { useState } from 'react';
import { Box } from '@mui/material';
import { styled } from '@mui/material/styles';
import PolygonOverlay from './PolygonOverlay';
import LocationMarker from './LocationMarker';
import CoordinateDisplay from './CoordinateDisplay';

const MapWrapper = styled(Box)(({ theme }) => ({
  position: 'relative',
  width: '100%',
  height: '100%',
  backgroundColor: theme.palette.grey[900],
  backgroundImage: `url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='100' height='100' viewBox='0 0 100 100'%3E%3Cg fill-opacity='0.1'%3E%3Cpolygon fill='%23000' points='50 0 60 40 100 50 60 60 50 100 40 60 0 50 40 40'/%3E%3C/g%3E%3C/svg%3E")`,
  overflow: 'hidden',
}));

const SatelliteMapBackground = styled(Box)({
  position: 'absolute',
  top: 0,
  left: 0,
  right: 0,
  bottom: 0,
  backgroundImage: 'url("https://images.unsplash.com/photo-1446776653964-20c1d3a81b06?w=1200&h=800&fit=crop")',
  backgroundSize: 'cover',
  backgroundPosition: 'center',
  opacity: 0.8,
});

interface MapContainerProps {
  polygonData: Array<{
    id: string;
    name: string;
    coordinates: Array<[number, number]>;
    area: string;
    status: string;
  }>;
  locationMarkers: Array<{
    id: string;
    name: string;
    position: { lat: number; lng: number };
    type: 'building' | 'plaza' | 'gate' | 'construction';
  }>;
  mapCenter: { lat: number; lng: number };
  selectedPolygons: string[];
  onPolygonSelect?: (polygonId: string) => void;
  onMarkerClick?: (markerId: string) => void;
}

const MapContainer: React.FC<MapContainerProps> = ({
  polygonData,
  locationMarkers,
  mapCenter,
  selectedPolygons,
  onPolygonSelect,
  onMarkerClick
}) => {
  // Convert coordinates to pixel positions (simplified for demo)
  const coordToPixel = (lat: number, lng: number) => {
    const centerLat = 30.540675;
    const centerLng = 114.365421;
    const scale = 20000; // Adjust for zoom level
    
    const x = (lng - centerLng) * scale + 50; // Center at 50%
    const y = (centerLat - lat) * scale + 50; // Center at 50%
    
    return { x: `${x}%`, y: `${y}%` };
  };

  return (
    <MapWrapper>
      <SatelliteMapBackground />
      
      {/* Render polygons */}
      {polygonData.map((polygon) => {
        const topLeft = coordToPixel(polygon.coordinates[0][1], polygon.coordinates[0][0]);
        const bottomRight = coordToPixel(polygon.coordinates[2][1], polygon.coordinates[2][0]);
        
        return (
          <PolygonOverlay
            key={polygon.id}
            id={polygon.id}
            name={polygon.name}
            area={polygon.area}
            isSelected={selectedPolygons.includes(polygon.id)}
            style={{
              left: topLeft.x,
              top: topLeft.y,
              width: `${parseFloat(bottomRight.x) - parseFloat(topLeft.x)}%`,
              height: `${parseFloat(bottomRight.y) - parseFloat(topLeft.y)}%`,
            }}
            onClick={onPolygonSelect}
          />
        );
      })}
      
      {/* Render location markers */}
      {locationMarkers.map((marker) => {
        const position = coordToPixel(marker.position.lat, marker.position.lng);
        
        return (
          <LocationMarker
            key={marker.id}
            id={marker.id}
            name={marker.name}
            type={marker.type}
            position={marker.position}
            style={{
              left: position.x,
              top: position.y,
            }}
            onClick={onMarkerClick}
          />
        );
      })}
      
      <CoordinateDisplay lat={mapCenter.lat} lng={mapCenter.lng} />
    </MapWrapper>
  );
};

export default MapContainer;
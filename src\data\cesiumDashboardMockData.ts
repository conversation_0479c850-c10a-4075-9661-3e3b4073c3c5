// Mock data for the 3D data management system

// Data for global state store
export const mockStore = {
  selectedPolygons: ["polygon-1", "polygon-2"],
  mapCenter: { lat: 30.540675, lng: 114.365421 },
  zoomLevel: 16,
  activeLayer: "satellite" as const,
  sidePanel: {
    isOpen: true,
    activeTab: "layers" as const
  }
};

// Data returned by API queries
export const mockQuery = {
  polygonData: [
    {
      id: "polygon-1",
      name: "塔吊施工点名称",
      coordinates: [
        [114.3650, 30.5410],
        [114.3655, 30.5410],
        [114.3655, 30.5405],
        [114.3650, 30.5405]
      ],
      area: "7亩",
      status: "active" as const
    },
    {
      id: "polygon-2", 
      name: "塔吊施工点名称路段",
      coordinates: [
        [114.3660, 30.5408],
        [114.3668, 30.5408],
        [114.3668, 30.5400],
        [114.3660, 30.5400]
      ],
      area: "财富名园",
      status: "active" as const
    }
  ],
  locationMarkers: [
    {
      id: "marker-1",
      name: "西个廊友",
      position: { lat: 30.5406, lng: 114.3648 },
      type: "building" as const
    },
    {
      id: "marker-2",
      name: "百悦山庄广场",
      position: { lat: 30.5408, lng: 114.3645 },
      type: "plaza" as const
    },
    {
      id: "marker-3",
      name: "宜门",
      position: { lat: 30.5404, lng: 114.3652 },
      type: "gate" as const
    }
  ]
};

// Data passed as props to the root component
export const mockRootProps = {
  initialMapCenter: { lat: 30.540675, lng: 114.365421 },
  initialZoom: 16,
  systemTitle: "实景三维数据管理系统",
  systemSubtitle: "Real scene 3D data management system"
};
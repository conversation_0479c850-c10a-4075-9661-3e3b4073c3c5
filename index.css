/* Font imports for Chinese characters */
@import url('https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap');

:root {
  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

#root {
  margin: auto;
}

/* Global styles for the 3D data management system */
body {
  margin: 0;
  padding: 0;
  font-family: "Roboto", "Microsoft YaHei", "SimHei", sans-serif;
  background-color: #1A1A1A;
  color: #FFFFFF;
}

/* Cesium specific styles */
.cesium-viewer {
  width: 100%;
  height: 100%;
  margin: 0;
  padding: 0;
  overflow: hidden;
}

.cesium-widget-credits {
  display: none !important;
}

/* Custom scrollbar for dark theme */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: #2D2D2D;
}

::-webkit-scrollbar-thumb {
  background: #555;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #777;
}
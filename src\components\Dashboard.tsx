import React, { useState } from 'react';
import { Box, Stack } from '@mui/material';
import { styled } from '@mui/material/styles';
import HeaderNavigation from './navigation/HeaderNavigation';
import MapContainer from './map/MapContainer';
import SidePanel from './controls/SidePanel';
import { mockQuery } from '../data/cesiumDashboardMockData';

const DashboardContainer = styled(Box)(({ theme }) => ({
  height: '100vh',
  backgroundColor: theme.palette.background.default,
  overflow: 'hidden',
}));

const MainContent = styled(Box)({
  position: 'relative',
  flex: 1,
  height: 'calc(100vh - 64px)', // Subtract header height
});

interface DashboardProps {
  systemTitle: string;
  systemSubtitle: string;
  initialMapCenter: { lat: number; lng: number };
  initialZoom?: number;
}

const Dashboard: React.FC<DashboardProps> = ({
  systemTitle,
  systemSubtitle,
  initialMapCenter,
  initialZoom = 16
}) => {
  const [selectedPolygons, setSelectedPolygons] = useState<string[]>(['polygon-1']);
  const [mapCenter, setMapCenter] = useState(initialMapCenter);
  const [zoomLevel, setZoomLevel] = useState(initialZoom);
  const [activeTab, setActiveTab] = useState(0);

  const handlePolygonSelect = (polygonId: string) => {
    setSelectedPolygons(prev => 
      prev.includes(polygonId) 
        ? prev.filter(id => id !== polygonId)
        : [...prev, polygonId]
    );
  };

  const handleMarkerClick = (markerId: string) => {
    console.log('Marker clicked:', markerId);
  };

  const handleZoomIn = () => {
    setZoomLevel(prev => Math.min(prev + 1, 20));
  };

  const handleZoomOut = () => {
    setZoomLevel(prev => Math.max(prev - 1, 1));
  };

  const handleSearch = () => {
    console.log('Search triggered');
  };

  const handleLayersToggle = () => {
    console.log('Layers panel toggled');
  };

  const handleTabChange = (value: number) => {
    setActiveTab(value);
  };

  return (
    <DashboardContainer>
      <Stack direction="column" sx={{ height: '100%' }}>
        <HeaderNavigation
          systemTitle={systemTitle}
          systemSubtitle={systemSubtitle}
          onTabChange={handleTabChange}
        />
        
        <MainContent>
          <MapContainer
            polygonData={mockQuery.polygonData}
            locationMarkers={mockQuery.locationMarkers}
            mapCenter={mapCenter}
            selectedPolygons={selectedPolygons}
            onPolygonSelect={handlePolygonSelect}
            onMarkerClick={handleMarkerClick}
          />
          
          <SidePanel
            onSearch={handleSearch}
            onLayersToggle={handleLayersToggle}
            onZoomIn={handleZoomIn}
            onZoomOut={handleZoomOut}
          />
        </MainContent>
      </Stack>
    </DashboardContainer>
  );
};

export default Dashboard;
import React from 'react';
import { Box, Typography } from '@mui/material';
import { styled } from '@mui/material/styles';

const PolygonContainer = styled(Box)(({ theme }) => ({
  position: 'absolute',
  border: `2px solid ${theme.palette.secondary.main}`,
  backgroundColor: `${theme.palette.secondary.main}20`,
  cursor: 'pointer',
  zIndex: 50,
  '&:hover': {
    backgroundColor: `${theme.palette.secondary.main}30`,
    borderColor: theme.palette.secondary.light,
  },
  '&.selected': {
    backgroundColor: `${theme.palette.secondary.main}40`,
    borderColor: theme.palette.secondary.light,
    boxShadow: `0 0 10px ${theme.palette.secondary.main}`,
  },
}));

const PolygonLabel = styled(Box)(({ theme }) => ({
  position: 'absolute',
  top: '50%',
  left: '50%',
  transform: 'translate(-50%, -50%)',
  backgroundColor: theme.palette.background.paper,
  padding: theme.spacing(0.5, 1),
  borderRadius: theme.shape.borderRadius,
  border: `1px solid ${theme.palette.grey[700]}`,
}));

interface PolygonOverlayProps {
  id: string;
  name: string;
  area: string;
  isSelected?: boolean;
  style?: React.CSSProperties;
  onClick?: (id: string) => void;
}

const PolygonOverlay: React.FC<PolygonOverlayProps> = ({
  id,
  name,
  area,
  isSelected = false,
  style,
  onClick
}) => {
  const handleClick = () => {
    onClick?.(id);
  };

  return (
    <PolygonContainer
      className={isSelected ? 'selected' : ''}
      style={style}
      onClick={handleClick}
    >
      <PolygonLabel>
        <Typography variant="caption" sx={{ display: 'block', fontSize: '0.7rem' }}>
          {name}
        </Typography>
        <Typography variant="caption" sx={{ fontSize: '0.6rem', opacity: 0.8 }}>
          {area}
        </Typography>
      </PolygonLabel>
    </PolygonContainer>
  );
};

export default PolygonOverlay;
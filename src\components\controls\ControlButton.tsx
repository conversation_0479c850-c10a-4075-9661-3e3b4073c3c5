import React from 'react';
import { IconButton, Tooltip } from '@mui/material';
import { styled } from '@mui/material/styles';

const StyledIconButton = styled(IconButton)(({ theme }) => ({
  backgroundColor: theme.palette.primary.main,
  color: theme.palette.primary.contrastText,
  width: 48,
  height: 48,
  margin: theme.spacing(1),
  '&:hover': {
    backgroundColor: theme.palette.primary.dark,
    transform: 'scale(1.05)',
  },
  transition: 'all 0.2s ease-in-out',
  boxShadow: theme.shadows[4],
}));

interface ControlButtonProps {
  icon: React.ReactNode;
  tooltip: string;
  onClick?: () => void;
  disabled?: boolean;
}

const ControlButton: React.FC<ControlButtonProps> = ({
  icon,
  tooltip,
  onClick,
  disabled = false
}) => {
  return (
    <Tooltip title={tooltip} placement="left">
      <span>
        <StyledIconButton
          onClick={onClick}
          disabled={disabled}
          size="medium"
        >
          {icon}
        </StyledIconButton>
      </span>
    </Tooltip>
  );
};

export default ControlButton;
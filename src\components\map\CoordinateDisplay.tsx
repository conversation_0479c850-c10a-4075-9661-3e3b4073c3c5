import React from 'react';
import { Box, Typography } from '@mui/material';
import { styled } from '@mui/material/styles';
import { formatCoordinate } from '../../utils/formatters';

const CoordinateContainer = styled(Box)(({ theme }) => ({
  position: 'absolute',
  bottom: theme.spacing(2),
  right: theme.spacing(2),
  backgroundColor: theme.palette.background.paper,
  padding: theme.spacing(1, 2),
  borderRadius: theme.shape.borderRadius,
  border: `1px solid ${theme.palette.grey[700]}`,
  zIndex: 1000,
}));

interface CoordinateDisplayProps {
  lat: number;
  lng: number;
}

const CoordinateDisplay: React.FC<CoordinateDisplayProps> = ({ lat, lng }) => {
  return (
    <CoordinateContainer>
      <Typography variant="caption" sx={{ display: 'block' }}>
        {formatCoordinate(lng, 'lng')} {formatCoordinate(lat, 'lat')}
      </Typography>
    </CoordinateContainer>
  );
};

export default CoordinateDisplay;
import React from 'react';
import { Chip, Box } from '@mui/material';
import { styled } from '@mui/material/styles';
import LocationOnIcon from '@mui/icons-material/LocationOn';
import BusinessIcon from '@mui/icons-material/Business';
import ParkIcon from '@mui/icons-material/Park';
import DoorBackIcon from '@mui/icons-material/DoorBack';

const MarkerContainer = styled(Box)(({ theme }) => ({
  position: 'absolute',
  transform: 'translate(-50%, -100%)',
  zIndex: 100,
}));

const StyledChip = styled(Chip)(({ theme }) => ({
  backgroundColor: theme.palette.background.paper,
  color: theme.palette.text.primary,
  fontSize: '0.75rem',
  height: 24,
  '& .MuiChip-icon': {
    fontSize: '1rem',
    color: theme.palette.primary.main,
  },
  '&:hover': {
    backgroundColor: theme.palette.primary.main,
    color: theme.palette.primary.contrastText,
    '& .MuiChip-icon': {
      color: theme.palette.primary.contrastText,
    },
  },
}));

interface LocationMarkerProps {
  id: string;
  name: string;
  type: 'building' | 'plaza' | 'gate' | 'construction';
  position: { lat: number; lng: number };
  style?: React.CSSProperties;
  onClick?: (id: string) => void;
}

const getMarkerIcon = (type: string) => {
  switch (type) {
    case 'building':
      return <BusinessIcon />;
    case 'plaza':
      return <ParkIcon />;
    case 'gate':
      return <DoorBackIcon />;
    default:
      return <LocationOnIcon />;
  }
};

const LocationMarker: React.FC<LocationMarkerProps> = ({
  id,
  name,
  type,
  style,
  onClick
}) => {
  const handleClick = () => {
    onClick?.(id);
  };

  return (
    <MarkerContainer style={style}>
      <StyledChip
        icon={getMarkerIcon(type)}
        label={name}
        size="small"
        onClick={handleClick}
        clickable
      />
    </MarkerContainer>
  );
};

export default LocationMarker;